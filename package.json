{"name": "aureus-alliance-holdings", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 8000", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 8000", "start": "vite preview --host 0.0.0.0 --port 8000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "validate:css": "node scripts/validate-css-classes.js", "validate:all": "npm run validate:css && npm run lint"}, "dependencies": {"@supabase/supabase-js": "^2.50.3", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "dotenv": "^17.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "tailwindcss": "^3.4.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "serve": "^14.2.4", "typescript": "~5.7.2", "vite": "^6.2.0"}}