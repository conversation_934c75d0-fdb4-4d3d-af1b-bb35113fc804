import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser, supabase } from '../lib/supabase'
import SharePurchaseFlow from './SharePurchaseFlow'

// Note: Using main supabase client instead of creating additional instances
// to avoid "Multiple GoTrueClient instances" warning
import TelegramConnectionModal from './TelegramConnectionModal'
import { MarketingToolkit } from './MarketingToolkit'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
  onNavigate?: (section: string) => void
}

// Modern Icons
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

// Dividend Calculator Component
interface DividendCalculatorProps {
  userShares: number
}

const DividendCalculator: React.FC<DividendCalculatorProps> = ({ userShares }) => {
  const [inputs, setInputs] = useState({
    landHa: 25,
    avgGravelThickness: 0.8,
    inSituGrade: 0.9,
    recoveryFactor: 70,
    goldPriceUsdPerKg: 100000,
    opexPercent: 45,
    dividendPayoutPercent: 50
  })

  const [calculated, setCalculated] = useState({
    numPlants: 0,
    annualRevenue: 0,
    annualEbit: 0,
    annualGoldKg: 0,
    dividendPerShare: 0,
    userAnnualDividend: 0
  })

  // Constants from the main calculator
  const PLANT_CAPACITY_TPH = 200
  const EFFECTIVE_HOURS_PER_DAY = 20
  const OPERATING_DAYS_PER_YEAR = 330
  const BULK_DENSITY_T_PER_M3 = 1.8
  const HA_PER_PLANT = 25
  const TOTAL_SHARES = 1400000

  useEffect(() => {
    const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent } = inputs

    // Calculate based on user's land selection (using same formula as homepage calculator)
    const numPlants = landHa / HA_PER_PLANT
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR
    const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000
    const annualRevenue = annualGoldKg * goldPriceUsdPerKg
    const annualOperatingCost = annualRevenue * (opexPercent / 100)
    const annualEbit = annualRevenue - annualOperatingCost

    // Calculate dividend per share dynamically: Full EBIT ÷ Total Shares (100% payout like homepage)
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0
    const userAnnualDividend = dividendPerShare * userShares

    setCalculated({
      numPlants,
      annualRevenue,
      annualEbit,
      annualGoldKg,
      dividendPerShare,
      userAnnualDividend
    })
  }, [inputs, userShares])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setInputs(prev => ({ ...prev, [name]: parseFloat(value) || 0 }))
  }

  const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat('en-US', options).format(num)
  }

  const LAND_SIZE_OPTIONS = Array.from({ length: 40 }, (_, i) => (i + 1) * 25)

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      marginBottom: '30px',
      border: '1px solid #374151'
    }}>
      <h3 style={{
        color: '#F59E0B',
        fontSize: '20px',
        fontWeight: 'bold',
        margin: '0 0 8px 0',
        textAlign: 'center'
      }}>
        Dividend Calculator
      </h3>
      <p style={{
        color: '#9CA3AF',
        fontSize: '14px',
        textAlign: 'center',
        margin: '0 0 24px 0'
      }}>
        Configure your scenario and see how dividends are calculated based on your {userShares.toLocaleString()} shares
      </p>

      {/* Input Parameters */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginBottom: '24px'
      }}>
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>LAND SIZE</h4>
          <select
            name="landHa"
            value={inputs.landHa}
            onChange={handleInputChange}
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          >
            {LAND_SIZE_OPTIONS.map(size => (
              <option key={size} value={size}>{size} ha</option>
            ))}
          </select>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Corresponds to {formatNumber(inputs.landHa / HA_PER_PLANT * (TOTAL_SHARES / 10), { maximumFractionDigits: 0 })} shares
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>YOUR SHARES</h4>
          <div style={{
            padding: '8px 12px',
            backgroundColor: 'rgba(17, 24, 39, 0.4)',
            border: '1px solid #374151',
            borderRadius: '6px',
            color: '#9CA3AF',
            fontSize: '16px'
          }}>
            {userShares.toLocaleString()}
          </div>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Total project shares: {TOTAL_SHARES.toLocaleString()}
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>GOLD PRICE</h4>
          <input
            type="number"
            name="goldPriceUsdPerKg"
            value={inputs.goldPriceUsdPerKg}
            onChange={handleInputChange}
            step="1000"
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          />
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            USD per kilogram
          </p>
        </div>
      </div>

      {/* Results */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>🏭</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.numPlants, { maximumFractionDigits: 1 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Plants for Land</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>💰</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualRevenue, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Land Revenue Potential</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualEbit, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Land EBIT Potential</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>🥇</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualGoldKg, { maximumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>kg/year</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>💎</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            ${formatNumber(calculated.userAnnualDividend, { minimumFractionDigits: 2 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Your Annual Dividend</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(168, 85, 247, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>📈</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            ${formatNumber(calculated.dividendPerShare, { minimumFractionDigits: 4 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Dividend Per Share</div>
        </div>
      </div>

      {/* Technical Parameters Display */}
      <div style={{
        marginTop: '24px',
        padding: '16px',
        backgroundColor: 'rgba(55, 65, 81, 0.3)',
        borderRadius: '8px',
        border: '1px solid #374151'
      }}>
        <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>TECHNICAL PARAMETERS</h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          fontSize: '13px'
        }}>
          <div>
            <span style={{ color: '#9CA3AF' }}>Gravel Thickness:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.avgGravelThickness}m</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>In-situ Grade:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.inSituGrade} g/t</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Recovery Factor:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.recoveryFactor}%</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Operating Cost:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.opexPercent}%</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Dividend Payout:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.dividendPayoutPercent}%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser, onNavigate }) => {
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false)
  const [showTelegramModal, setShowTelegramModal] = useState(false)
  const [telegramUser, setTelegramUser] = useState<any>(null)
  const [telegramLoading, setTelegramLoading] = useState(false)

  // Demo mode: Only allow full access to user ID 4
  const isDemoUser = () => {
    const userId = user?.database_user?.id || user?.id
    console.log('🎭 Demo mode check - User ID:', userId, 'Is demo user:', userId === 4)
    return userId === 4
  }

  // Helper function to get referral username
  const getReferralUsername = (user: any) => {
    // Priority order: username -> database_user.username -> email prefix -> fallback
    if (user?.username) {
      return user.username
    }
    if (user?.database_user?.username) {
      return user.database_user.username
    }
    if (user?.email) {
      const emailPrefix = user.email.split('@')[0]
      // Clean up email prefix to be URL-friendly
      return emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
    }
    if (user?.database_user?.email) {
      const emailPrefix = user.database_user.email.split('@')[0]
      return emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
    }
    // Final fallback using user ID
    const userId = user?.database_user?.id || user?.id
    return userId ? `user_${userId.toString().slice(-8)}` : 'user_unknown'
  }

  // Dashboard data states
  const [dashboardData, setDashboardData] = useState({
    totalShares: 0,
    shareValue: 0,
    futureDividends: 0,
    // Separate commission types like Telegram bot
    usdtCommissions: {
      totalEarned: 0,
      available: 0,
      escrowed: 0
    },
    shareCommissions: {
      totalShares: 0,
      currentValue: 0
    },
    accountBalance: 0,
    referralCount: 0,
    monthlyEarnings: 0
  })
  const [dataLoading, setDataLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      console.log('🔄 Loading current user...')
      const currentUser = await getCurrentUser()
      console.log('👤 Current user loaded:', currentUser)
      setUser(currentUser)

      // Check if user has linked Telegram account and load dashboard data
      if (currentUser?.database_user?.id) {
        console.log('✅ User has database_user.id:', currentUser.database_user.id)
        await checkTelegramConnection(currentUser.database_user.id)
        await loadDashboardData(currentUser.database_user.id)
      } else if (currentUser?.id) {
        console.log('⚠️ User has id but no database_user.id, using user.id:', currentUser.id)
        await checkTelegramConnection(currentUser.id)
        await loadDashboardData(currentUser.id)
      } else {
        console.log('❌ No valid user ID found in currentUser:', currentUser)
      }
    } catch (error) {
      console.error('❌ Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadDashboardData = async (userId: number) => {
    try {
      setDataLoading(true)
      console.log('🔄 Loading dashboard data for user:', userId, typeof userId)

      // Validate userId
      if (!userId || isNaN(userId)) {
        console.error('❌ Invalid user ID:', userId)
        setDataLoading(false)
        return
      }

      // Get current share price from active phase - use service role for this public data
      const { data: currentPhase, error: phaseError } = await supabase
        .from('investment_phases')
        .select('price_per_share')
        .eq('is_active', true)
        .single()

      if (phaseError) {
        console.error('Error fetching current phase:', phaseError)
      }

      const currentSharePrice = currentPhase?.price_per_share || 5.00
      console.log('📊 Current share price:', currentSharePrice)

      // Fetch user shares from aureus_share_purchases
      console.log('🔍 Fetching shares for user ID:', userId, typeof userId)
      const { data: sharesPurchases, error: sharesError } = await supabase
        .from('aureus_share_purchases')
        .select('shares_purchased, total_amount, status')
        .eq('user_id', userId)
        .eq('status', 'active')

      if (sharesError) {
        console.error('❌ Error fetching shares:', sharesError)
        console.error('Error details:', {
          code: sharesError.code,
          message: sharesError.message,
          hint: sharesError.hint,
          details: sharesError.details,
          userId,
          table: 'aureus_share_purchases'
        })
      } else {
        console.log('✅ Shares data:', sharesPurchases)
      }

      // Fetch commission balance - get all fields like Telegram bot
      console.log('🔍 Fetching commission balance for user ID:', userId, typeof userId)
      const { data: commissionBalance, error: commissionError } = await supabase
        .from('commission_balances')
        .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount, total_withdrawn')
        .eq('user_id', userId)
        .single()

      if (commissionError && commissionError.code !== 'PGRST116') {
        console.error('❌ Error fetching commission balance:', commissionError)
        console.error('Error details:', {
          code: commissionError.code,
          message: commissionError.message,
          hint: commissionError.hint,
          details: commissionError.details,
          userId,
          table: 'commission_balances'
        })
      } else if (commissionError?.code === 'PGRST116') {
        console.log('ℹ️ No commission balance record found (expected for new users)')
      } else {
        console.log('✅ Commission balance data:', commissionBalance)
      }

      // Fetch referral count
      console.log('🔍 Fetching referrals for user ID:', userId)
      const { data: referrals, error: referralsError } = await supabase
        .from('referrals')
        .select('id')
        .eq('referrer_id', userId)

      if (referralsError) {
        console.error('❌ Error fetching referrals:', referralsError)
        console.error('Query details:', { userId, table: 'referrals' })
      } else {
        console.log('✅ Referrals data:', referrals)
      }

      // Calculate purchased shares totals
      const purchasedShares = sharesPurchases?.reduce((sum, purchase) => sum + purchase.shares_purchased, 0) || 0
      const purchasedSharesValue = sharesPurchases?.reduce((sum, purchase) => sum + purchase.total_amount, 0) || 0

      // Commission data - separate USDT and Share commissions like Telegram bot
      const totalEarnedUSDT = commissionBalance?.total_earned_usdt || 0
      const totalEarnedShares = commissionBalance?.total_earned_shares || 0
      const availableUSDT = commissionBalance?.usdt_balance || 0
      const availableShares = commissionBalance?.share_balance || 0
      const escrowedAmount = commissionBalance?.escrowed_amount || 0

      // Calculate TOTAL shares owned (purchased + commission shares)
      const totalShares = purchasedShares + availableShares
      console.log('📊 Share calculation:', {
        purchasedShares,
        availableShares,
        totalShares,
        currentSharePrice
      })

      // Calculate TOTAL share value using current share price
      const shareValue = totalShares * currentSharePrice
      const shareCommissionValue = totalEarnedShares * currentSharePrice

      // Account balance = available USDT + available shares value
      const accountBalance = availableUSDT + (availableShares * currentSharePrice)

      const referralCount = referrals?.length || 0

      // Calculate future dividends based on total owned shares using dynamic EBIT calculation
      // Use the same calculation logic as the homepage calculator for consistency

      // Constants for calculation (same as homepage calculator)
      const PLANT_CAPACITY_TPH = 200
      const EFFECTIVE_HOURS_PER_DAY = 20
      const OPERATING_DAYS_PER_YEAR = 330
      const BULK_DENSITY_T_PER_M3 = 1.8
      const HA_PER_PLANT = 25
      const TOTAL_SHARES_CALC = 1400000

      // Default parameters for future dividend calculation (25ha baseline)
      const baselineLandHa = 25
      const avgGravelThickness = 0.8
      const inSituGrade = 0.9
      const recoveryFactor = 70
      const goldPriceUsdPerKg = 100000
      const opexPercent = 45

      // Calculate baseline EBIT for 25ha (1 plant) using same formula as homepage calculator
      const numPlants = baselineLandHa / HA_PER_PLANT
      const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR
      const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000
      const annualRevenue = annualGoldKg * goldPriceUsdPerKg
      const annualOperatingCost = annualRevenue * (opexPercent / 100)
      const baselineEbit = annualRevenue - annualOperatingCost

      // Calculate dividend per share dynamically: Full EBIT ÷ Total Shares (100% payout like homepage)
      const dividendPerShare = TOTAL_SHARES_CALC > 0 ? baselineEbit / TOTAL_SHARES_CALC : 0
      const futureDividends = totalShares * dividendPerShare

      setDashboardData({
        totalShares,
        shareValue,
        futureDividends,
        // Separate commission types like Telegram bot
        usdtCommissions: {
          totalEarned: totalEarnedUSDT,
          available: availableUSDT,
          escrowed: escrowedAmount
        },
        shareCommissions: {
          totalShares: totalEarnedShares,
          currentValue: shareCommissionValue
        },
        accountBalance,
        referralCount,
        monthlyEarnings: (totalEarnedUSDT + shareCommissionValue) / 12 // Rough monthly average
      })

      console.log('✅ Dashboard data loaded:', {
        totalShares,
        shareValue,
        futureDividends,
        usdtCommissions: {
          totalEarned: totalEarnedUSDT,
          available: availableUSDT,
          escrowed: escrowedAmount
        },
        shareCommissions: {
          totalShares: totalEarnedShares,
          currentValue: shareCommissionValue
        },
        accountBalance,
        referralCount,
        currentSharePrice
      })

    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setDataLoading(false)
    }
  }

  const checkTelegramConnection = async (userId: number) => {
    try {
      console.log('🔍 Checking Telegram connection for user:', userId)

      // First check if user already has telegram_id in their record
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('telegram_id, username, full_name')
        .eq('id', userId)
        .single()

      if (userError) {
        console.error('Error fetching user data:', userError)
        return
      }

      console.log('👤 User data:', userData)

      if (userData.telegram_id) {
        // User already has Telegram connected, fetch Telegram user details
        console.log('✅ User already has Telegram ID:', userData.telegram_id)

        const { data: telegramUserData, error: telegramError } = await supabase
          .from('users')
          .select('id, telegram_id, username, full_name')
          .eq('telegram_id', userData.telegram_id)
          .single()

        if (!telegramError && telegramUserData) {
          console.log('✅ Found Telegram user data:', telegramUserData)
          setTelegramUser(telegramUserData)
        } else {
          console.log('⚠️ Could not fetch Telegram user details, but telegram_id exists')
          // Set basic telegram user info from the user record
          setTelegramUser({
            id: userData.telegram_id,
            telegram_id: userData.telegram_id,
            username: userData.username,
            full_name: userData.full_name
          })
        }
      } else {
        console.log('❌ No Telegram connection found for user')
        setTelegramUser(null)
      }
    } catch (error) {
      console.error('Error checking Telegram connection:', error)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  const handleTelegramConnect = async (telegramId: string) => {
    if (!user?.database_user?.id) {
      throw new Error('User not authenticated')
    }

    // Validate telegram_id format (should be numeric)
    if (!/^\d{8,12}$/.test(telegramId)) {
      throw new Error('Invalid Telegram ID format')
    }

    // For now, just show a message that this should be done during registration
    alert('Please use the registration page to link your Telegram account. This ensures proper data integration.')
    setShowTelegramModal(false)
  }

  const handleTelegramDisconnect = async () => {
    if (!user?.database_user?.id || !telegramUser) {
      return
    }

    try {
      // Unlink the accounts by removing the user_id from telegram_users record
      const { error: updateError } = await supabase
        .from('telegram_users')
        .update({
          user_id: null,
          is_registered: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.database_user.id)

      if (updateError) {
        console.error('Error unlinking telegram_users:', updateError)
        throw new Error('Failed to unlink telegram_users record')
      }

      // Also clear telegram_id from users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          telegram_id: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.database_user.id)

      if (userUpdateError) {
        console.error('Error clearing users telegram_id:', userUpdateError)
        // Don't fail the whole process, but log the error
        console.warn('⚠️ Failed to clear telegram_id from users table, but telegram_users unlink was successful')
      }

      setTelegramUser(null)
      console.log('✅ Telegram account disconnected successfully')
    } catch (error) {
      console.error('❌ Telegram disconnect error:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%)',
      color: 'white',
      display: 'flex'
    }}>
      {/* Modern Sidebar */}
      <div style={{
        width: '280px',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderRight: '1px solid #374151',
        display: 'flex',
        flexDirection: 'column',
        backdropFilter: 'blur(10px)'
      }}>
        {/* Logo */}
        <div style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'black', fontWeight: 'bold', fontSize: '18px' }}>A</span>
            </div>
            <span style={{ color: 'white', fontWeight: '600', fontSize: '20px' }}>Aureus</span>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{ flex: 1, padding: '16px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            backgroundColor: 'rgba(37, 99, 235, 0.2)',
            borderRadius: '12px',
            color: '#60a5fa',
            border: '1px solid rgba(37, 99, 235, 0.3)',
            marginBottom: '8px'
          }}>
            <DashboardIcon />
            <span style={{ fontWeight: '500' }}>Dashboard</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer',
            marginBottom: '8px'
          }}>
            <SharesIcon />
            <span>My Shares</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer',
            marginBottom: '8px'
          }}>
            <PortfolioIcon />
            <span>Portfolio</span>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 16px',
            color: '#9ca3af',
            borderRadius: '12px',
            cursor: 'pointer'
          }}>
            <CommissionIcon />
            <span>Dividends</span>
          </div>
        </nav>

        {/* User Info at Bottom */}
        <div style={{ padding: '16px', borderTop: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: 'linear-gradient(135deg, #60a5fa 0%, #a855f7 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ color: 'white', fontSize: '14px', fontWeight: '500' }}>
                  {(user?.username || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p style={{ color: 'white', fontSize: '14px', fontWeight: '500', margin: 0 }}>
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              style={{
                background: 'none',
                border: 'none',
                color: '#9ca3af',
                cursor: 'pointer',
                padding: '4px'
              }}
              title="Logout"
            >
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, padding: '32px' }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px' }}>
          <div>
            <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', margin: 0 }}>Dashboard</h1>
            <p style={{ color: '#9ca3af', marginTop: '8px', margin: 0 }}>
              Welcome back, {user?.username || user?.email?.split('@')[0]}!
            </p>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              backdropFilter: 'blur(10px)',
              padding: '16px 24px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <span style={{ color: '#9ca3af', fontSize: '14px', display: 'block' }}>Account Balance</span>
              <p style={{ color: 'white', fontWeight: 'bold', fontSize: '18px', margin: 0 }}>
                ${dataLoading ? '...' : dashboardData.accountBalance.toFixed(2)}
              </p>
            </div>

            {/* Prominent Logout Button */}
            <button
              onClick={handleLogout}
              style={{
                background: 'linear-gradient(135deg, #dc2626, #b91c1c)',
                border: 'none',
                color: 'white',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(220, 38, 38, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 16px rgba(220, 38, 38, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
              }}
              title="Sign out of your account"
            >
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </div>

        {/* Demo Mode Banner */}
        {!isDemoUser() && (
          <div style={{
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            border: '1px solid rgba(245, 158, 11, 0.3)',
            borderRadius: '12px',
            padding: '20px',
            marginBottom: '32px',
            textAlign: 'center'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px', marginBottom: '12px' }}>
              <span style={{ fontSize: '24px' }}>🚧</span>
              <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
                Demo Mode Active
              </h3>
              <span style={{ fontSize: '24px' }}>🚧</span>
            </div>
            <p style={{ color: '#d97706', fontSize: '14px', margin: '0 0 8px 0' }}>
              This is a demonstration version. Share purchasing and marketing tools are temporarily disabled.
            </p>
            <p style={{ color: '#92400e', fontSize: '12px', margin: 0 }}>
              Full functionality will be available after our development phase is complete.
            </p>
          </div>
        )}

        {/* Simple Overview */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#3b82f6', margin: '0 0 5px 0' }}>
              {dataLoading ? '...' : dashboardData.totalShares.toLocaleString()}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Gold Shares Owned</p>
          </div>

          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0 0 5px 0' }}>
              ${dataLoading ? '...' : dashboardData.shareValue.toLocaleString()}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Share Value</p>
          </div>

          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#f59e0b', margin: '0 0 5px 0' }}>
              ${dataLoading ? '...' : dashboardData.futureDividends.toLocaleString()}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Future Dividends</p>
          </div>

          {/* USDT Commissions - Separate like Telegram bot */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#10b981', margin: '0 0 5px 0' }}>
              ${dataLoading ? '...' : dashboardData.usdtCommissions.totalEarned.toFixed(2)}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>USDT Commissions</p>
          </div>

          {/* Share Commissions - Separate like Telegram bot */}
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            border: '1px solid #374151'
          }}>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#a855f7', margin: '0 0 5px 0' }}>
              {dataLoading ? '...' : dashboardData.shareCommissions.totalShares.toLocaleString()}
            </p>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>Share Commissions</p>
          </div>
        </div>

        {/* Dividend Calculator - Shows how dividends are calculated based on user's shares */}
        <DividendCalculator userShares={dashboardData.totalShares} />

        {/* Commission Balance Details - Match Telegram Bot Format */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '30px',
          border: '1px solid #374151'
        }}>
          <h3 style={{
            color: 'white',
            fontSize: '20px',
            fontWeight: 'bold',
            margin: '0 0 20px 0',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            💰 COMMISSION BALANCE
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '24px'
          }}>
            {/* USDT Commissions Section */}
            <div style={{
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(16, 185, 129, 0.3)'
            }}>
              <h4 style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', margin: '0 0 12px 0' }}>
                💵 USDT COMMISSIONS:
              </h4>
              <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Total Earned:</strong> ${dataLoading ? '...' : dashboardData.usdtCommissions.totalEarned.toFixed(2)} USDT
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Available for Withdrawal:</strong> ${dataLoading ? '...' : dashboardData.usdtCommissions.available.toFixed(2)} USDT
                </div>
                <div>
                  • <strong>Currently Escrowed:</strong> ${dataLoading ? '...' : dashboardData.usdtCommissions.escrowed.toFixed(2)} USDT
                </div>
              </div>
            </div>

            {/* Share Commissions Section */}
            <div style={{
              backgroundColor: 'rgba(168, 85, 247, 0.1)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(168, 85, 247, 0.3)'
            }}>
              <h4 style={{ color: '#a855f7', fontSize: '16px', fontWeight: '600', margin: '0 0 12px 0' }}>
                📈 SHARE COMMISSIONS:
              </h4>
              <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Total Shares Earned:</strong> {dataLoading ? '...' : dashboardData.shareCommissions.totalShares.toLocaleString()} shares
                </div>
                <div style={{ marginBottom: '4px' }}>
                  • <strong>Current Value:</strong> ${dataLoading ? '...' : dashboardData.shareCommissions.currentValue.toFixed(2)} USD
                </div>
                <div>
                  • <strong>Status:</strong> Active in portfolio
                </div>
              </div>
            </div>
          </div>

          {/* Commission Summary */}
          <div style={{
            marginTop: '20px',
            padding: '16px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '8px',
            border: '1px solid #4b5563'
          }}>
            <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', margin: '0 0 12px 0' }}>
              📊 COMMISSION SUMMARY:
            </h4>
            <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
              <div style={{ marginBottom: '4px' }}>
                • <strong>Total Commission Value:</strong> ${dataLoading ? '...' : (dashboardData.usdtCommissions.totalEarned + dashboardData.shareCommissions.currentValue).toFixed(2)}
              </div>
              <div>
                • <strong>Commission Rate:</strong> 15% USDT + 15% Shares
              </div>
            </div>
          </div>
        </div>

        {/* Main Actions */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <button
            onClick={isDemoUser() ? () => setShowPurchaseFlow(true) : undefined}
            disabled={!isDemoUser()}
            style={{
              background: isDemoUser()
                ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
                : 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
              borderRadius: '12px',
              padding: '30px',
              textAlign: 'center',
              border: 'none',
              cursor: isDemoUser() ? 'pointer' : 'not-allowed',
              color: 'white',
              opacity: isDemoUser() ? 1 : 0.6,
              position: 'relative'
            }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', margin: '0 0 8px 0' }}>
              Buy Gold Shares
              {!isDemoUser() && <span style={{ fontSize: '16px', marginLeft: '8px' }}>🔒</span>}
            </h2>
            <p style={{ fontSize: '14px', opacity: 0.9, margin: 0 }}>
              {isDemoUser()
                ? 'Purchase shares in our gold mining operation'
                : 'Available after development phase'
              }
            </p>
          </button>

          <button style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '12px',
            padding: '30px',
            textAlign: 'center',
            border: '2px solid #374151',
            cursor: 'pointer',
            color: 'white'
          }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', margin: '0 0 8px 0' }}>
              View My Shares
            </h2>
            <p style={{ fontSize: '14px', color: '#9ca3af', margin: 0 }}>
              Check your current share holdings
            </p>
          </button>
        </div>

        {/* Marketing Toolkit - Only available for demo user */}
        {isDemoUser() ? (
          <MarketingToolkit
            user={user}
            getReferralUsername={getReferralUsername}
          />
        ) : (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.9)',
            borderRadius: '16px',
            padding: '32px',
            marginBottom: '32px',
            border: '1px solid #374151',
            textAlign: 'center'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <span style={{ fontSize: '48px', display: 'block', marginBottom: '16px' }}>🚀</span>
              <h2 style={{
                fontSize: '28px',
                fontWeight: 'bold',
                color: '#6b7280',
                margin: '0 0 12px 0'
              }}>
                Marketing Toolkit
              </h2>
              <p style={{ color: '#9ca3af', fontSize: '16px', margin: '0 0 20px 0' }}>
                Professional marketing tools will be available after our development phase
              </p>
              <div style={{
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                border: '1px solid rgba(245, 158, 11, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                display: 'inline-block'
              }}>
                <p style={{ color: '#f59e0b', fontSize: '14px', margin: 0, fontWeight: '500' }}>
                  🔒 Coming Soon - Advanced referral tracking, campaign management, and analytics
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Account Info */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '20px',
          border: '1px solid #374151'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px',
            alignItems: 'center'
          }}>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Account</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>{user?.email}</p>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Status</p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                <p style={{ color: '#22c55e', fontWeight: 'bold', margin: 0 }}>Active</p>
              </div>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Dividends Start</p>
              <p style={{ color: '#f59e0b', fontWeight: 'bold', margin: 0 }}>March 2026</p>
            </div>
            <div>
              <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Member Since</p>
              <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
          </div>
        </div>

        {/* Telegram Connection Section */}
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          marginTop: '20px',
          border: '1px solid #374151'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                backgroundColor: '#0088cc',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px'
              }}>
                📱
              </div>
              <h3 style={{ color: 'white', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
                Telegram Integration
              </h3>
            </div>
          </div>

          {telegramUser ? (
            // Connected State
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#22c55e', borderRadius: '50%' }}></span>
                <span style={{ color: '#22c55e', fontWeight: 'bold', fontSize: '14px' }}>Connected to Telegram</span>
              </div>
              <div style={{ marginBottom: '16px' }}>
                <p style={{ fontSize: '14px', color: '#9ca3af', margin: '0 0 4px 0' }}>Connected Account</p>
                <p style={{ color: 'white', fontWeight: '500', margin: 0 }}>
                  {telegramUser.first_name} {telegramUser.last_name}
                  {telegramUser.username && ` (@${telegramUser.username})`}
                </p>
                <p style={{ fontSize: '12px', color: '#6b7280', margin: '2px 0 0 0' }}>
                  ID: {telegramUser.telegram_id}
                </p>
              </div>
              <button
                onClick={handleTelegramDisconnect}
                style={{
                  padding: '8px 16px',
                  backgroundColor: 'transparent',
                  border: '1px solid #ef4444',
                  borderRadius: '6px',
                  color: '#ef4444',
                  fontSize: '12px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Disconnect
              </button>
            </div>
          ) : (
            // Not Connected State
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#6b7280', borderRadius: '50%' }}></span>
                <span style={{ color: '#6b7280', fontSize: '14px' }}>Not connected to Telegram</span>
              </div>
              <p style={{ fontSize: '14px', color: '#9ca3af', marginBottom: '16px', lineHeight: '1.5' }}>
                Connect your Telegram account to sync your existing bot data and enable cross-platform features.
              </p>
              <button
                onClick={() => setShowTelegramModal(true)}
                style={{
                  padding: '12px 20px',
                  backgroundColor: '#0088cc',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#0077b3';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#0088cc';
                }}
              >
                📱 Connect to Telegram
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Share Purchase Flow Modal */}
      {showPurchaseFlow && (
        <SharePurchaseFlow
          user={user}
          onClose={() => setShowPurchaseFlow(false)}
        />
      )}

      {/* Telegram Connection Modal */}
      <TelegramConnectionModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
        onConnect={handleTelegramConnect}
        loading={telegramLoading}
      />


    </div>
  )
}
